"""
FastAPI主应用 - Cube1 Group Backend
🎯 核心价值：基础FastAPI应用框架
🚀 功能：应用初始化、中间件配置、基础路由
⚡ 特性：自动API文档、CORS支持、错误处理
"""

from contextlib import asynccontextmanager
import logging
import time

from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse

from app.api.v1.router import api_router
from app.core.exceptions import AppException
from app.core.logging import setup_logging

# 设置日志
setup_logging()
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时执行
    logger.info("🚀 启动 Cube1 Backend 应用...")
    logger.info("✅ 基础应用初始化完成")

    # 这里可以添加其他启动任务
    # - 数据库连接初始化
    # - Redis连接初始化
    # - 缓存预热
    # - 后台任务启动

    yield

    # 关闭时执行
    logger.info("🔄 关闭 Cube1 Backend 应用...")
    logger.info("✅ 应用已关闭")


# 创建FastAPI应用实例
app = FastAPI(
    title="Cube1 Group Backend",
    version="0.1.0",
    description="""
    ## Cube1 Group Backend API

    🎯 **基础框架**
    - FastAPI 基础应用
    - 等待前端需求确认后开发具体功能

    🚀 **技术特性**
    - FastAPI 框架
    - 自动API文档生成
    - CORS支持
    - 错误处理

    📚 **API文档**
    - [Swagger UI](/docs) - 交互式API文档
    - [ReDoc](/redoc) - 美观的API文档
    """,
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan,
    debug=True,
)


# === 中间件配置 ===

# CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 开发阶段允许所有来源
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# === 自定义中间件 ===


@app.middleware("http")
async def request_timing_middleware(request: Request, call_next):
    """请求计时中间件"""
    start_time = time.time()

    # 处理请求
    response = await call_next(request)

    # 计算处理时间
    process_time = time.time() - start_time
    response.headers["X-Process-Time"] = str(process_time)

    # 记录慢请求
    if process_time > 1.0:  # 超过1秒的请求
        logger.warning(f"慢请求: {request.method} {request.url} - {process_time:.2f}s")

    return response


@app.middleware("http")
async def request_logging_middleware(request: Request, call_next):
    """请求日志中间件"""
    # 记录请求开始
    logger.info(
        f"请求开始: {request.method} {request.url} "
        f"- Client: {request.client.host if request.client else 'unknown'}"
    )

    try:
        response = await call_next(request)

        # 记录响应
        logger.info(
            f"请求完成: {request.method} {request.url} " f"- Status: {response.status_code}"
        )

        return response

    except Exception as e:
        # 记录错误
        logger.error(f"请求错误: {request.method} {request.url} " f"- Error: {str(e)}")
        raise


# === 全局异常处理 ===


@app.exception_handler(AppException)
async def app_exception_handler(request: Request, exc: AppException):
    """应用异常处理器"""
    logger.error(f"应用异常: {exc.message} - {exc.details}")

    return JSONResponse(
        status_code=exc.status_code,
        content={
            "success": False,
            "error": exc.message,
            "details": exc.details,
            "error_code": exc.error_code,
            "timestamp": time.time(),
        },
    )


@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    """通用异常处理器"""
    logger.error(f"未处理异常: {str(exc)}", exc_info=True)

    return JSONResponse(
        status_code=500,
        content={
            "success": False,
            "error": "内部服务器错误",
            "details": str(exc) if settings.app.debug else "请联系管理员",
            "timestamp": time.time(),
        },
    )


# === 路由注册 ===

# 注册API路由
app.include_router(api_router, prefix="/api/v1", tags=["API v1"])


# === 根路径和健康检查 ===


@app.get("/", tags=["Root"])
async def root():
    """根路径 - 应用信息"""
    return {
        "message": "🎯 Cube1 Group Backend API",
        "version": "0.1.0",
        "environment": "development",
        "docs": "/docs",
        "redoc": "/redoc",
        "status": "running",
        "timestamp": time.time(),
    }


@app.get("/health", tags=["Health"])
async def health_check():
    """健康检查端点"""
    return {
        "status": "healthy",
        "service": "cube1-backend",
        "version": "0.1.0",
        "environment": "development",
        "timestamp": time.time(),
    }


if __name__ == "__main__":
    import uvicorn

    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info",
    )
